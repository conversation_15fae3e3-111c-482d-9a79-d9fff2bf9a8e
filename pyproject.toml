[project]
name = "deepagents"
version = "0.0.3"
description = "General purpose 'deep agent' with sub-agent spawning, todo list capabilities, and mock file system. Built on LangGraph."
readme = "README.md"
license = { text = "MIT" }
requires-python = ">=3.11,<4.0"
dependencies = [
    "langgraph>=0.2.6",
    "langchain-anthropic>=0.1.23",
    "langchain>=0.2.14",
]


[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["deepagents"]
[tool.setuptools.package-dir]
"deepagents" = "src/deepagents"

[tool.setuptools.package-data]
"*" = ["py.typed"]
