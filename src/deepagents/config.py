"""Configuration module for deepagents.

This module handles loading environment variables from .env files
and provides utilities for configuration management.
"""

import os
from pathlib import Path
from typing import Optional

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


def load_environment_variables(env_file: Optional[str] = None) -> None:
    """Load environment variables from .env file.
    
    Args:
        env_file: Path to the .env file. If None, looks for .env in current directory
                 and parent directories.
    """
    if not DOTENV_AVAILABLE:
        # If python-dotenv is not available, just return
        # Environment variables might be set through other means
        return
    
    if env_file:
        # Load specific .env file
        if os.path.exists(env_file):
            load_dotenv(env_file)
    else:
        # Look for .env file in current directory and parent directories
        current_dir = Path.cwd()
        
        # Check current directory first
        env_path = current_dir / ".env"
        if env_path.exists():
            load_dotenv(env_path)
            return
        
        # Check parent directories
        for parent in current_dir.parents:
            env_path = parent / ".env"
            if env_path.exists():
                load_dotenv(env_path)
                return


def get_api_key(key_name: str, required: bool = True) -> Optional[str]:
    """Get an API key from environment variables.
    
    Args:
        key_name: Name of the environment variable
        required: Whether the key is required. If True and key is missing,
                 raises ValueError.
    
    Returns:
        The API key value or None if not found and not required.
        
    Raises:
        ValueError: If required=True and the key is not found.
    """
    value = os.environ.get(key_name)
    
    if required and not value:
        raise ValueError(
            f"Required API key '{key_name}' not found in environment variables. "
            f"Please set it in your .env file or environment."
        )
    
    return value


def check_required_api_keys() -> dict[str, bool]:
    """Check if required API keys are available.
    
    Returns:
        Dictionary mapping API key names to their availability status.
    """
    required_keys = {
        "ANTHROPIC_API_KEY": "Required for Claude models",
        "TAVILY_API_KEY": "Required for web search functionality",
    }
    
    optional_keys = {
        "LANGCHAIN_API_KEY": "Optional for LangSmith tracing",
        "OPENAI_API_KEY": "Optional for OpenAI models",
    }
    
    status = {}
    
    for key, description in required_keys.items():
        status[key] = {
            "available": bool(os.environ.get(key)),
            "required": True,
            "description": description
        }
    
    for key, description in optional_keys.items():
        status[key] = {
            "available": bool(os.environ.get(key)),
            "required": False,
            "description": description
        }
    
    return status


# Load environment variables when this module is imported
load_environment_variables()
